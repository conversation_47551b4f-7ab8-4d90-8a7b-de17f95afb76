{"rustc": 5357548097637079788, "features": "[]", "declared_features": "[]", "target": 8970836321080188892, "profile": 9280017546041637478, "path": 16808470999612928342, "deps": [[5921074888975346911, "gloo_utils", false, 3764142996890182169], [6946689283190175495, "wasm_bindgen", false, 14465521140166482764], [8008191657135824715, "thiserror", false, 12438249790608091954], [8264480821543757363, "web_sys", false, 10785000613826564197], [9003359908906038687, "js_sys", false, 13114632575426945191], [9689903380558560274, "serde", false, 4788658676475767664], [15367738274754116744, "serde_json", false, 6637315096987123201]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/gloo-storage-ed44a73317fd2ddb/dep-lib-gloo_storage", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}