{"rustc": 5357548097637079788, "features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "declared_features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "target": 17962624438068670222, "profile": 9280017546041637478, "path": 10780843913085627624, "deps": [[4106074096146763267, "gloo_events", false, 15433635194701937189], [5921074888975346911, "gloo_utils", false, 3764142996890182169], [6946689283190175495, "wasm_bindgen", false, 14465521140166482764], [8008191657135824715, "thiserror", false, 12438249790608091954], [8264480821543757363, "web_sys", false, 10785000613826564197], [9689903380558560274, "serde", false, 4788658676475767664], [9920160576179037441, "getrandom", false, 291413135635022262], [11261232116272131900, "serde_wasm_bindgen", false, 16116182824211569108], [16542808166767769916, "serde_urlencoded", false, 1561133537241071104]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/gloo-history-32dc98fcb566d985/dep-lib-gloo_history", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}