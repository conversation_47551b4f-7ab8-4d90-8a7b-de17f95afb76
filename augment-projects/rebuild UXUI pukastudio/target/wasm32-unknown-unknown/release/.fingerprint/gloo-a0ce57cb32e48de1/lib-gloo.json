{"rustc": 5357548097637079788, "features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"futures\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "declared_features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"futures\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "target": 15424014265002599538, "profile": 9280017546041637478, "path": 17076279696429071933, "deps": [[932682457716462887, "gloo_worker", false, 5120436935396984515], [3019852804355383212, "gloo_net", false, 5167581009786993004], [4106074096146763267, "gloo_events", false, 15433635194701937189], [5921074888975346911, "gloo_utils", false, 3764142996890182169], [10534580474014942609, "gloo_dialogs", false, 3362786301786622337], [12805895089852270265, "gloo_timers", false, 17529554109581172789], [13555662950551470143, "gloo_storage", false, 7057247273057495287], [14803534212725985479, "gloo_file", false, 16403736170105235822], [15525454374806154151, "gloo_render", false, 4110623484795978569], [16754746302422839792, "gloo_console", false, 2878220934601086335], [17123018067225436055, "gloo_history", false, 12481507745708314703]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/gloo-a0ce57cb32e48de1/dep-lib-gloo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}