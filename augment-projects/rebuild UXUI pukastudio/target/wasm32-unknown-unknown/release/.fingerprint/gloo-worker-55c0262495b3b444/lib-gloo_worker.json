{"rustc": 5357548097637079788, "features": "[\"default\"]", "declared_features": "[\"default\", \"futures\"]", "target": 15706070843846846069, "profile": 9280017546041637478, "path": 2376002852515578073, "deps": [[65234016722529558, "bincode", false, 843504476907744216], [6946689283190175495, "wasm_bindgen", false, 14465521140166482764], [8264480821543757363, "web_sys", false, 10785000613826564197], [9003359908906038687, "js_sys", false, 13114632575426945191], [9689903380558560274, "serde", false, 4788658676475767664], [12469029679378729817, "gloo_console", false, 7564105466097625763], [13843926109129797205, "anymap2", false, 3588581096286286506], [15917073803248137067, "wasm_bindgen_futures", false, 7396645549772836259], [18122101786498147437, "gloo_utils", false, 13955549114035088463]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/gloo-worker-55c0262495b3b444/dep-lib-gloo_worker", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}