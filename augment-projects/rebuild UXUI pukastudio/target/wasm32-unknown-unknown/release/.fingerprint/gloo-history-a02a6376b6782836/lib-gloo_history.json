{"rustc": 5357548097637079788, "features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "declared_features": "[\"default\", \"query\", \"serde_urlencoded\", \"thiserror\"]", "target": 7162787421944370751, "profile": 9280017546041637478, "path": 10987214441416679720, "deps": [[6946689283190175495, "wasm_bindgen", false, 14465521140166482764], [8008191657135824715, "thiserror", false, 12438249790608091954], [8264480821543757363, "web_sys", false, 10785000613826564197], [9689903380558560274, "serde", false, 4788658676475767664], [10542362878077003840, "serde_wasm_bindgen", false, 12285381817486392601], [12069273292196623910, "gloo_events", false, 3588774359546332759], [16542808166767769916, "serde_urlencoded", false, 1561133537241071104], [18122101786498147437, "gloo_utils", false, 13955549114035088463]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/gloo-history-a02a6376b6782836/dep-lib-gloo_history", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}