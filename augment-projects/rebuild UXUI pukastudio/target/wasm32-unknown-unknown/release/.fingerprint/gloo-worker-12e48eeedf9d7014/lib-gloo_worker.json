{"rustc": 5357548097637079788, "features": "[\"default\"]", "declared_features": "[\"default\", \"futures\"]", "target": 11217130877511374870, "profile": 9280017546041637478, "path": 9243685073002756712, "deps": [[65234016722529558, "bincode", false, 843504476907744216], [2706460456408817945, "futures", false, 4238105919603945519], [5921074888975346911, "gloo_utils", false, 3764142996890182169], [6946689283190175495, "wasm_bindgen", false, 14465521140166482764], [8008191657135824715, "thiserror", false, 12438249790608091954], [8264480821543757363, "web_sys", false, 10785000613826564197], [9003359908906038687, "js_sys", false, 13114632575426945191], [9689903380558560274, "serde", false, 4788658676475767664], [12919484467758300183, "gloo_worker_macros", false, 9847130128288505738], [14997362514532562728, "pinned", false, 9514355900847237846], [15917073803248137067, "wasm_bindgen_futures", false, 7396645549772836259]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/gloo-worker-12e48eeedf9d7014/dep-lib-gloo_worker", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}