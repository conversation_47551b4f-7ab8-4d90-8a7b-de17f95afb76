{"rustc": 5357548097637079788, "features": "[\"default\", \"eventsource\", \"futures-channel\", \"futures-core\", \"futures-sink\", \"http\", \"json\", \"pin-project\", \"serde\", \"serde_json\", \"websocket\"]", "declared_features": "[\"default\", \"eventsource\", \"futures-channel\", \"futures-core\", \"futures-sink\", \"http\", \"json\", \"pin-project\", \"serde\", \"serde_json\", \"websocket\"]", "target": 7289951416308014359, "profile": 9280017546041637478, "path": 2430389916362185178, "deps": [[1811549171721445101, "futures_channel", false, 18326752789789074135], [4405182208873388884, "http", false, 2442152406539503920], [5921074888975346911, "gloo_utils", false, 3764142996890182169], [6264115378959545688, "pin_project", false, 4585419770632885778], [6946689283190175495, "wasm_bindgen", false, 14465521140166482764], [7013762810557009322, "futures_sink", false, 2884210174081480610], [7620660491849607393, "futures_core", false, 11425230681996105955], [8008191657135824715, "thiserror", false, 12438249790608091954], [8264480821543757363, "web_sys", false, 10785000613826564197], [9003359908906038687, "js_sys", false, 13114632575426945191], [9689903380558560274, "serde", false, 4788658676475767664], [15367738274754116744, "serde_json", false, 6637315096987123201], [15917073803248137067, "wasm_bindgen_futures", false, 7396645549772836259]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/gloo-net-1284e7e482edaaa5/dep-lib-gloo_net", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}