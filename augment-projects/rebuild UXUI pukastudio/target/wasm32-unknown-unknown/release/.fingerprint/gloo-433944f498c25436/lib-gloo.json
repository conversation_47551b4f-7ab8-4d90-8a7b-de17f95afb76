{"rustc": 5357548097637079788, "features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "declared_features": "[\"console\", \"default\", \"dialogs\", \"events\", \"file\", \"futures\", \"gloo-console\", \"gloo-dialogs\", \"gloo-events\", \"gloo-file\", \"gloo-history\", \"gloo-net\", \"gloo-render\", \"gloo-storage\", \"gloo-timers\", \"gloo-utils\", \"gloo-worker\", \"history\", \"net\", \"render\", \"storage\", \"timers\", \"utils\", \"worker\"]", "target": 17023954276131510380, "profile": 9280017546041637478, "path": 5745164446932299719, "deps": [[5028561147853581999, "gloo_timers", false, 9575159521854742645], [9329111706356929223, "gloo_file", false, 15784861427360238192], [9979345710825402490, "gloo_dialogs", false, 9520788505932985157], [10340280370311140941, "gloo_worker", false, 5064961641323403452], [12069273292196623910, "gloo_events", false, 3588774359546332759], [12469029679378729817, "gloo_console", false, 7564105466097625763], [13787904963389927206, "gloo_storage", false, 2907419394479412614], [15119418339232312804, "gloo_history", false, 1284647433818000135], [17073337587916847516, "gloo_render", false, 400777301579770706], [17548459073812404046, "gloo_net", false, 6925413368440431945], [18122101786498147437, "gloo_utils", false, 13955549114035088463]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/release/.fingerprint/gloo-433944f498c25436/dep-lib-gloo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}