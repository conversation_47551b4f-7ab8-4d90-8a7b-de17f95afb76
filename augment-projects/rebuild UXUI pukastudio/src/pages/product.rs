use crate::components::{layout::Container, product::ProductDetail, ui::LoadingSpinner};
use crate::services::api::fetch_product;
use crate::types::{AppError, Product};
use wasm_bindgen_futures::spawn_local;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct ProductPageProps {
    pub id: String,
}

#[derive(Clone, PartialEq)]
enum ProductState {
    Loading,
    Loaded(Product),
    Error(String),
}

#[function_component(ProductPage)]
pub fn product_page(props: &ProductPageProps) -> Html {
    let product_id = props.id.clone();
    let state = use_state(|| ProductState::Loading);

    {
        let state = state.clone();
        let product_id = product_id.clone();
        use_effect_with(product_id.clone(), move |_| {
            let state = state.clone();
            let product_id = product_id.clone();

            spawn_local(async move {
                match fetch_product(&product_id).await {
                    Ok(product) => {
                        state.set(ProductState::Loaded(product));
                    }
                    Err(error) => {
                        state.set(ProductState::Error(error.to_string()));
                    }
                }
            });

            || ()
        });
    }

    html! {
        <div class="ultra-compact">
            <Container>
                {match (*state).clone() {
                    ProductState::Loading => {
                        html! {
                            <div class="text-center py-20">
                                <LoadingSpinner size={48} />
                                <p class="text-gray-500 dark:text-gray-400 mt-4">
                                    {"Đang tải thông tin sản phẩm..."}
                                </p>
                            </div>
                        }
                    }
                    ProductState::Loaded(product) => {
                        html! { <ProductDetail product={product.clone()} /> }
                    }
                    ProductState::Error(error) => {
                        html! {
                            <div class="text-center py-20">
                                <div class="text-red-600 dark:text-red-400 mb-4">
                                    <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                </div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                                    {"Không tìm thấy sản phẩm"}
                                </h1>
                                <p class="text-gray-600 dark:text-gray-300 mb-8">
                                    {format!("Sản phẩm với ID '{}' không tồn tại.", props.id)}
                                </p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    {format!("Lỗi: {}", error)}
                                </p>
                            </div>
                        }
                    }
                }}
            </Container>
        </div>
    }
}
