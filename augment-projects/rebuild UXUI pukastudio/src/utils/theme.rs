use wasm_bindgen::prelude::*;
use web_sys::{window, MediaQueryList, Storage};
use yew::prelude::*;

/// Theme preference enum
#[derive(Clone, Copy, PartialEq, Debug)]
pub enum Theme {
    Light,
    Dark,
    System,
}

impl Theme {
    /// Convert theme to string for storage
    pub fn to_string(&self) -> &'static str {
        match self {
            Theme::Light => "light",
            Theme::Dark => "dark",
            Theme::System => "system",
        }
    }

    /// Parse theme from string
    pub fn from_string(s: &str) -> Self {
        match s {
            "light" => Theme::Light,
            "dark" => Theme::Dark,
            "system" => Theme::System,
            _ => Theme::System, // Default to system
        }
    }

    /// Get the effective theme (resolves system preference)
    pub fn effective_theme(&self) -> Theme {
        match self {
            Theme::System => {
                if prefers_dark_scheme() {
                    Theme::Dark
                } else {
                    Theme::Light
                }
            }
            theme => *theme,
        }
    }

    /// Get CSS class for the theme
    pub fn css_class(&self) -> &'static str {
        match self.effective_theme() {
            Theme::Dark => "dark",
            _ => "",
        }
    }
}

/// Theme context state
#[derive(Clone, PartialEq)]
pub struct ThemeContext {
    pub theme: Theme,
    pub set_theme: Callback<Theme>,
}

/// Theme context provider component
#[derive(Properties, PartialEq)]
pub struct ThemeProviderProps {
    pub children: Children,
}

#[function_component(ThemeProvider)]
pub fn theme_provider(props: &ThemeProviderProps) -> Html {
    // Initialize theme from localStorage or system preference
    let theme = use_state(|| load_theme_preference().unwrap_or(Theme::System));

    // Create callback for setting theme
    let set_theme = {
        let theme = theme.clone();
        Callback::from(move |new_theme: Theme| {
            theme.set(new_theme);
            save_theme_preference(new_theme);
            apply_theme_to_document(new_theme);
        })
    };

    // Apply theme on mount and when theme changes
    {
        let current_theme = *theme;
        use_effect_with(current_theme, move |theme| {
            apply_theme_to_document(*theme);
            || ()
        });
    }

    // Listen for system theme changes
    {
        let theme = theme.clone();
        let set_theme = set_theme.clone();
        use_effect_with((), move |_| {
            let closure = Closure::wrap(Box::new(move |_: web_sys::Event| {
                if *theme == Theme::System {
                    apply_theme_to_document(*theme);
                }
            }) as Box<dyn FnMut(_)>);

            if let Some(window) = window() {
                if let Ok(Some(media_query)) = window.match_media("(prefers-color-scheme: dark)") {
                    let _ = media_query.add_event_listener_with_callback(
                        "change",
                        closure.as_ref().unchecked_ref(),
                    );
                    closure.forget(); // Keep the closure alive
                }
            }

            || ()
        });
    }

    let context = ThemeContext {
        theme: *theme,
        set_theme,
    };

    html! {
        <ContextProvider<ThemeContext> context={context}>
            { for props.children.iter() }
        </ContextProvider<ThemeContext>>
    }
}

/// Check if user prefers dark color scheme
pub fn prefers_dark_scheme() -> bool {
    window()
        .and_then(|w| w.match_media("(prefers-color-scheme: dark)").ok())
        .and_then(|mql| mql)
        .map(|mql| mql.matches())
        .unwrap_or(false)
}

/// Load theme preference from localStorage
pub fn load_theme_preference() -> Option<Theme> {
    get_local_storage()
        .and_then(|storage| storage.get_item("theme").ok())
        .flatten()
        .map(|theme_str| Theme::from_string(&theme_str))
}

/// Save theme preference to localStorage
pub fn save_theme_preference(theme: Theme) {
    if let Some(storage) = get_local_storage() {
        let _ = storage.set_item("theme", theme.to_string());
    }
}

/// Apply theme to document element
pub fn apply_theme_to_document(theme: Theme) {
    if let Some(window) = window() {
        if let Some(document) = window.document() {
            if let Some(html_element) = document.document_element() {
                let class_list = html_element.class_list();

                // Remove existing theme classes
                let _ = class_list.remove_1("dark");

                // Add new theme class if needed
                let css_class = theme.css_class();
                if !css_class.is_empty() {
                    let _ = class_list.add_1(css_class);
                }
            }
        }
    }
}

/// Get localStorage reference
fn get_local_storage() -> Option<Storage> {
    window().and_then(|w| w.local_storage().ok()).flatten()
}
