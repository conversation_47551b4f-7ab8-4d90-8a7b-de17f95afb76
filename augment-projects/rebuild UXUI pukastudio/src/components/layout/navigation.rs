use crate::Route;
use yew::prelude::*;
use yew_router::prelude::*;

#[derive(Properties, PartialEq)]
pub struct BreadcrumbProps {
    pub items: Vec<BreadcrumbItem>,
    #[prop_or_default]
    pub class: Classes,
}

#[derive(Clone, PartialEq)]
pub struct BreadcrumbItem {
    pub label: String,
    pub route: Option<Route>,
}

#[function_component(Breadcrumb)]
pub fn breadcrumb(props: &BreadcrumbProps) -> Html {
    let class = classes!(
        "flex",
        "items-center",
        "space-x-2",
        "text-sm",
        "bg-white",
        "dark:bg-gray-800",
        "border-b",
        "border-gray-100",
        "dark:border-gray-700",
        "py-1",
        "transition-colors",
        "duration-200",
        props.class.clone()
    );

    html! {
        <nav class={class} aria-label="Breadcrumb">
            <div class="max-w-7xl px-4 sm:px-6 lg:px-8">
                <ol class="flex items-center justify-start space-x-2">
                    { for props.items.iter().enumerate().map(|(index, item)| {
                        let is_last = index == props.items.len() - 1;

                        html! {
                            <li class="flex items-center">
                                if index > 0 {
                                    <svg class="flex-shrink-0 h-4 w-4 text-gray-300 dark:text-gray-600 mx-2 transition-colors duration-200" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                }

                                if let Some(route) = &item.route {
                                    <Link<Route>
                                        to={route.clone()}
                                        classes="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 text-sm hover:underline"
                                    >
                                        { &item.label }
                                    </Link<Route>>
                                } else if is_last {
                                    <span class="text-gray-900 dark:text-gray-100 font-semibold text-sm transition-colors duration-200">
                                        { &item.label }
                                    </span>
                                } else {
                                    <span class="text-gray-600 dark:text-gray-300 text-sm transition-colors duration-200">{ &item.label }</span>
                                }
                            </li>
                        }
                    }) }
                </ol>
            </div>
        </nav>
    }
}

#[derive(Properties, PartialEq)]
pub struct PaginationProps {
    pub current_page: u32,
    pub total_pages: u32,
    pub onchange: Callback<u32>,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(Pagination)]
pub fn pagination(props: &PaginationProps) -> Html {
    if props.total_pages <= 1 {
        return html! {};
    }

    let class = classes!(
        "flex",
        "items-center",
        "justify-center",
        "space-x-1",
        props.class.clone()
    );

    let prev_disabled = props.current_page <= 1;
    let next_disabled = props.current_page >= props.total_pages;

    let on_prev = {
        let onchange = props.onchange.clone();
        let current_page = props.current_page;
        Callback::from(move |_| {
            if current_page > 1 {
                onchange.emit(current_page - 1);
            }
        })
    };

    let on_next = {
        let onchange = props.onchange.clone();
        let current_page = props.current_page;
        let total_pages = props.total_pages;
        Callback::from(move |_| {
            if current_page < total_pages {
                onchange.emit(current_page + 1);
            }
        })
    };

    // Calculate visible page numbers
    let start_page = if props.current_page <= 3 {
        1
    } else if props.current_page >= props.total_pages - 2 {
        props.total_pages.saturating_sub(4)
    } else {
        props.current_page - 2
    };

    let end_page = (start_page + 4).min(props.total_pages);

    html! {
        <nav class={class} aria-label="Pagination">
            // Previous button
            <button
                onclick={on_prev}
                disabled={prev_disabled}
                class={classes!(
                    "px-3",
                    "py-2",
                    "text-sm",
                    "font-medium",
                    "text-gray-500",
                    "dark:text-gray-400",
                    "bg-white",
                    "dark:bg-gray-800",
                    "border",
                    "border-gray-300",
                    "dark:border-gray-600",
                    "rounded-l-md",
                    "hover:bg-gray-50",
                    "dark:hover:bg-gray-700",
                    "focus:outline-none",
                    "focus:ring-2",
                    "focus:ring-primary-500",
                    "dark:focus:ring-primary-400",
                    "transition-colors",
                    "duration-200",
                    prev_disabled.then_some("opacity-50 cursor-not-allowed")
                )}
            >
                {"Previous"}
            </button>

            // Page numbers
            { for (start_page..=end_page).map(|page| {
                let is_current = page == props.current_page;
                let onclick = {
                    let onchange = props.onchange.clone();
                    Callback::from(move |_| onchange.emit(page))
                };

                html! {
                    <button
                        onclick={onclick}
                        class={classes!(
                            "px-3",
                            "py-2",
                            "text-sm",
                            "font-medium",
                            "border",
                            "focus:outline-none",
                            "focus:ring-2",
                            "focus:ring-primary-500",
                            "dark:focus:ring-primary-400",
                            "transition-colors",
                            "duration-200",
                            if is_current {
                                "text-white bg-primary-600 dark:bg-primary-700 border-primary-600 dark:border-primary-700"
                            } else {
                                "text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                            }
                        )}
                    >
                        { page }
                    </button>
                }
            }) }

            // Next button
            <button
                onclick={on_next}
                disabled={next_disabled}
                class={classes!(
                    "px-3",
                    "py-2",
                    "text-sm",
                    "font-medium",
                    "text-gray-500",
                    "dark:text-gray-400",
                    "bg-white",
                    "dark:bg-gray-800",
                    "border",
                    "border-gray-300",
                    "dark:border-gray-600",
                    "rounded-r-md",
                    "hover:bg-gray-50",
                    "dark:hover:bg-gray-700",
                    "focus:outline-none",
                    "focus:ring-2",
                    "focus:ring-primary-500",
                    "dark:focus:ring-primary-400",
                    "transition-colors",
                    "duration-200",
                    next_disabled.then_some("opacity-50 cursor-not-allowed")
                )}
            >
                {"Next"}
            </button>
        </nav>
    }
}
