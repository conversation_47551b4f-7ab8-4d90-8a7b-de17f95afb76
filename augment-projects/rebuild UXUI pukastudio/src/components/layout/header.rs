use crate::components::{
    search::<PERSON>Bar,
    ui::{<PERSON>ton, ButtonSize, ButtonVariant, Icon, IconType, Theme<PERSON>oggle, ThemeToggleVariant},
};
use crate::Route;
use yew::prelude::*;
use yew_router::prelude::*;

#[function_component(Header)]
pub fn header() -> Html {
    let mobile_menu_open = use_state(|| false);
    let search_open = use_state(|| false);
    let search_should_focus = use_state(|| false);

    let toggle_mobile_menu = {
        let mobile_menu_open = mobile_menu_open.clone();
        Callback::from(move |_| {
            mobile_menu_open.set(!*mobile_menu_open);
        })
    };

    let toggle_search = {
        let search_open = search_open.clone();
        let search_should_focus = search_should_focus.clone();
        Callback::from(move |_| {
            let was_closed = !*search_open;
            search_open.set(!*search_open);

            // If we're opening the search, set focus flag
            if was_closed {
                search_should_focus.set(true);
            }
        })
    };

    html! {
        <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-colors duration-200">
            <div class="container-custom">
                <div class="flex items-center justify-between h-16">
                    // Enhanced Logo with better responsive design
                    <div class="flex items-center">
                        <Link<Route> to={Route::Home} classes="flex items-center space-x-3 group">
                            <div class="relative">
                                <img
                                    src="/images/logo.svg"
                                    alt="Puka Studio"
                                    class="h-12 w-auto transition-transform duration-200 group-hover:scale-105"
                                    loading="lazy"
                                />
                                <div class="absolute inset-0 bg-gradient-anime opacity-0 group-hover:opacity-10 rounded-lg transition-opacity duration-200"></div>
                            </div>
                            // Remove duplicate text - logo is sufficient
                            <span class="sr-only">{"Puka Studio - Cosplay Rental Studio"}</span>
                        </Link<Route>>
                    </div>

                    // Enhanced Desktop Navigation
                    <nav class="hidden lg:flex items-center space-x-1">
                        <Link<Route> to={Route::Home} classes="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
                            <div class="flex items-center space-x-2">
                                <Icon icon_type={IconType::Home} class="h-4 w-4" />
                                <span>{"Trang Chủ"}</span>
                            </div>
                        </Link<Route>>

                        <div class="relative group">
                            <Link<Route> to={Route::Catalog} classes="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 flex items-center">
                                <div class="flex items-center space-x-2">
                                    <Icon icon_type={IconType::ShoppingBag} class="h-4 w-4" />
                                    <span>{"Cho Thuê"}</span>
                                    <Icon icon_type={IconType::ChevronDown} class="ml-1 h-4 w-4 transition-transform duration-200 group-hover:rotate-180" />
                                </div>
                            </Link<Route>>

                            // Enhanced Dropdown Menu
                            <div class="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-anime border border-gray-100 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50">
                                <div class="p-4">
                                    <div class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">{"Danh mục sản phẩm"}</div>
                                    <div class="grid grid-cols-2 gap-2">
                                        <Link<Route>
                                            to={Route::CatalogCategory { category: "game".to_string() }}
                                            classes="p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 block"
                                        >
                                            <div class="flex items-center space-x-3">
                                                <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                                                    <Icon icon_type={IconType::GameController} class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900 dark:text-gray-100">{"Games"}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">{"Nhân vật game"}</div>
                                                </div>
                                            </div>
                                        </Link<Route>>

                                        <Link<Route>
                                            to={Route::CatalogCategory { category: "anime".to_string() }}
                                            classes="p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 block"
                                        >
                                            <div class="flex items-center space-x-3">
                                                <div class="p-2 bg-pink-100 dark:bg-pink-900/30 rounded-lg">
                                                    <Icon icon_type={IconType::Star} class="h-4 w-4 text-pink-600 dark:text-pink-400" />
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900 dark:text-gray-100">{"Anime"}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">{"Nhân vật anime"}</div>
                                                </div>
                                            </div>
                                        </Link<Route>>

                                        <Link<Route>
                                            to={Route::CatalogCategory { category: "vtuber".to_string() }}
                                            classes="p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 block"
                                        >
                                            <div class="flex items-center space-x-3">
                                                <div class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                                                    <Icon icon_type={IconType::Video} class="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900 dark:text-gray-100">{"VTuber"}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">{"Virtual YouTuber"}</div>
                                                </div>
                                            </div>
                                        </Link<Route>>

                                        <Link<Route>
                                            to={Route::CatalogCategory { category: "vocaloid".to_string() }}
                                            classes="p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 block"
                                        >
                                            <div class="flex items-center space-x-3">
                                                <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                                    <Icon icon_type={IconType::Music} class="h-4 w-4 text-green-600 dark:text-green-400" />
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900 dark:text-gray-100">{"Vocaloid"}</div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">{"Nhân vật Vocaloid"}</div>
                                                </div>
                                            </div>
                                        </Link<Route>>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <Link<Route> to={Route::Booking} classes="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
                            <div class="flex items-center space-x-2">
                                <Icon icon_type={IconType::Calendar} class="h-4 w-4" />
                                <span>{"Đặt Lịch"}</span>
                            </div>
                        </Link<Route>>

                        <Link<Route> to={Route::Contact} classes="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
                            <div class="flex items-center space-x-2">
                                <Icon icon_type={IconType::Phone} class="h-4 w-4" />
                                <span>{"Liên Hệ"}</span>
                            </div>
                        </Link<Route>>
                    </nav>

                    // Right side actions
                    <div class="flex items-center space-x-4">
                        // Search button
                        <button
                            onclick={toggle_search}
                            class="p-2 text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors"
                            aria-label="Search"
                        >
                            <Icon icon_type={IconType::Search} class="h-5 w-5" />
                        </button>

                        // Theme toggle
                        <ThemeToggle variant={ThemeToggleVariant::Button} />

                        // Cart button (future feature)
                        <button
                            class="p-2 text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors relative"
                            aria-label="Cart"
                        >
                            <Icon icon_type={IconType::ShoppingBag} class="h-5 w-5" />
                            <span class="absolute -top-1 -right-1 h-4 w-4 bg-secondary-500 text-white text-xs rounded-full flex items-center justify-center">
                                {"0"}
                            </span>
                        </button>

                        // Book Now CTA
                        <div class="hidden md:block">
                            <Link<Route> to={Route::Booking}>
                                <Button variant={ButtonVariant::Primary} size={ButtonSize::Small}>
                                    {"Đặt Ngay"}
                                </Button>
                            </Link<Route>>
                        </div>

                        // Mobile menu button
                        <button
                            onclick={toggle_mobile_menu}
                            class="lg:hidden p-2 text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 transition-colors"
                            aria-label="Menu"
                        >
                            if *mobile_menu_open {
                                <Icon icon_type={IconType::X} class="h-6 w-6" />
                            } else {
                                <Icon icon_type={IconType::Menu} class="h-6 w-6" />
                            }
                        </button>
                    </div>
                </div>

                // Search Bar (when open)
                if *search_open {
                    <div class="border-t border-gray-200 dark:border-gray-700 py-4 animate-slide-down">
                        <SearchBar
                            placeholder="Tìm kiếm trang phục, nhân vật..."
                            show_suggestions={true}
                            auto_focus={*search_should_focus}
                            on_focus_handled={
                                let search_should_focus = search_should_focus.clone();
                                Callback::from(move |_| {
                                    search_should_focus.set(false);
                                })
                            }
                        />
                    </div>
                }

                // Mobile Menu
                if *mobile_menu_open {
                    <div class="lg:hidden border-t border-gray-200 dark:border-gray-700 py-4 animate-slide-down">
                        <nav class="space-y-2">
                            <Link<Route>
                                to={Route::Home}
                                classes="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-600 dark:hover:text-primary-400 rounded-md transition-colors"
                            >
                                {"Trang Chủ"}
                            </Link<Route>>

                            <div class="px-4 py-2">
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">{"Cho Thuê"}</div>
                                <div class="space-y-1 ml-4">
                                    <Link<Route>
                                        to={Route::CatalogCategory { category: "game".to_string() }}
                                        classes="block py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                                    >
                                        {"Games"}
                                    </Link<Route>>
                                    <Link<Route>
                                        to={Route::CatalogCategory { category: "anime".to_string() }}
                                        classes="block py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                                    >
                                        {"Anime"}
                                    </Link<Route>>
                                    <Link<Route>
                                        to={Route::CatalogCategory { category: "vtuber".to_string() }}
                                        classes="block py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                                    >
                                        {"VTuber"}
                                    </Link<Route>>
                                    <Link<Route>
                                        to={Route::CatalogCategory { category: "vocaloid".to_string() }}
                                        classes="block py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                                    >
                                        {"Vocaloid"}
                                    </Link<Route>>
                                </div>
                            </div>

                            <Link<Route>
                                to={Route::Booking}
                                classes="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-600 dark:hover:text-primary-400 rounded-md transition-colors"
                            >
                                {"Đặt Lịch"}
                            </Link<Route>>

                            <Link<Route>
                                to={Route::Contact}
                                classes="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-600 dark:hover:text-primary-400 rounded-md transition-colors"
                            >
                                {"Liên Hệ"}
                            </Link<Route>>

                            <div class="px-4 pt-4">
                                <Link<Route> to={Route::Booking}>
                                    <Button variant={ButtonVariant::Primary} class="w-full">
                                        {"Đặt Ngay"}
                                    </Button>
                                </Link<Route>>
                            </div>
                        </nav>
                    </div>
                }
            </div>
        </header>
    }
}
