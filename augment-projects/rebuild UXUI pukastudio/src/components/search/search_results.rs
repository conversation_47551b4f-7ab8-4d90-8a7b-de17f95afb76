use crate::components::{
    layout::{Container, Grid, GridGap, Section},
    ui::{Badge, BadgeSize, BadgeVariant, Card, Icon, IconType, LoadingSpinner, ProductImage},
};
use crate::types::*;
use crate::Route;
use yew::prelude::*;
use yew_router::prelude::*;

#[derive(Properties, PartialEq)]
pub struct SearchResultsProps {
    pub results: Vec<SearchResult>,
    pub query: String,
    pub loading: bool,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(SearchResults)]
pub fn search_results(props: &SearchResultsProps) -> Html {
    if props.loading {
        return html! {
            <Section class="py-12">
                <Container>
                    <div class="flex justify-center">
                        <LoadingSpinner />
                    </div>
                </Container>
            </Section>
        };
    }

    if props.results.is_empty() {
        return html! {
            <Section class="py-12">
                <Container>
                    <div class="text-center">
                        <Icon icon_type={IconType::Search} class="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 mb-2">
                            {"Không tìm thấy kết quả"}
                        </h3>
                        <p class="text-gray-500 mb-6">
                            {format!("Không có sản phẩm nào phù hợp với từ khóa \"{}\"", props.query)}
                        </p>
                        <div class="space-y-2 text-sm text-gray-600">
                            <p>{"Gợi ý:"}</p>
                            <ul class="list-disc list-inside space-y-1">
                                <li>{"Kiểm tra lại chính tả"}</li>
                                <li>{"Thử sử dụng từ khóa khác"}</li>
                                <li>{"Sử dụng từ khóa ngắn gọn hơn"}</li>
                                <li>{"Tìm kiếm theo tên nhân vật hoặc franchise"}</li>
                            </ul>
                        </div>
                    </div>
                </Container>
            </Section>
        };
    }

    let results_html = props
        .results
        .iter()
        .map(|result| {
            html! {
                <SearchResultCard key={result.product.id.clone()} result={result.clone()} />
            }
        })
        .collect::<Html>();

    html! {
        <Section class={classes!("py-4", props.class.clone())}>
            <Container>
                // Search results header
                <div class="mb-3">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">
                        {"Kết quả tìm kiếm"}
                    </h2>
                    <p class="text-gray-600">
                        {format!("Tìm thấy {} sản phẩm cho \"{}\"", props.results.len(), props.query)}
                    </p>
                </div>

                // Results grid
                <Grid gap={GridGap::Large} class="grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {results_html}
                </Grid>
            </Container>
        </Section>
    }
}

#[derive(Properties, PartialEq)]
pub struct SearchResultCardProps {
    pub result: SearchResult,
}

#[function_component(SearchResultCard)]
pub fn search_result_card(props: &SearchResultCardProps) -> Html {
    let product = &props.result.product;
    let matched_fields = &props.result.matched_fields;

    // Create matched fields display
    let matched_fields_display = if !matched_fields.is_empty() {
        let field_names: Vec<String> = matched_fields
            .iter()
            .map(|field| match field.as_str() {
                "name" => "Tên sản phẩm".to_string(),
                "character_name" => "Tên nhân vật".to_string(),
                "franchise" => "Franchise".to_string(),
                "description" => "Mô tả".to_string(),
                "tags" => "Tags".to_string(),
                "category" => "Danh mục".to_string(),
                _ => field.clone(),
            })
            .collect();

        Some(field_names.join(", "))
    } else {
        None
    };

    // Get price display
    let price_display = if let Some(rental_option) = product.rental_options.first() {
        format!(
            "{}đ",
            rental_option
                .price
                .to_string()
                .chars()
                .rev()
                .enumerate()
                .fold(String::new(), |acc, (i, c)| {
                    if i > 0 && i % 3 == 0 {
                        format!("{}.{}", c, acc)
                    } else {
                        format!("{}{}", c, acc)
                    }
                })
        )
    } else {
        "Liên hệ".to_string()
    };

    html! {
        <Link<Route> to={Route::Product { id: product.id.clone() }}>
            <Card class="group hover:shadow-lg transition-all duration-300 h-full cursor-pointer border-l-4 border-l-transparent hover:border-l-primary-500">
                <div class="relative overflow-hidden">
                    <ProductImage
                        product_images={product.images.clone()}
                        alt={product.name.clone()}
                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        show_thumbnails={false}
                    />

                    // Enhanced Category badge with better dark mode visibility
                    <div class="absolute top-2 left-2 z-10">
                        <div class="bg-gradient-to-r from-primary-600 to-primary-700 dark:from-primary-500 dark:to-primary-600 text-white px-3 py-1.5 rounded-full text-xs font-semibold shadow-lg backdrop-blur-sm border border-white/20 dark:border-white/30 transition-all duration-200 hover:shadow-xl hover:scale-105">
                            {product.franchise.clone()}
                        </div>
                    </div>

                    // Relevance score (for debugging - can be removed in production)
                    if cfg!(debug_assertions) {
                        <div class="absolute top-2 right-2">
                            <Badge variant={BadgeVariant::Secondary} size={BadgeSize::Small}>
                                {format!("{:.1}", props.result.relevance_score)}
                            </Badge>
                        </div>
                    }
                </div>

                <div class="p-4">
                    // Product name
                    <h3 class="font-semibold text-gray-900 mb-1 line-clamp-2 group-hover:text-primary-600 transition-colors">
                        {&product.name}
                    </h3>

                    // Character name
                    <p class="text-sm text-gray-600 mb-2">
                        {&product.character_name}
                    </p>

                    // Matched fields indicator
                    if let Some(ref fields) = matched_fields_display {
                        <div class="mb-2">
                            <span class="text-xs text-primary-600 bg-primary-50 px-2 py-1 rounded-full">
                                {"Khớp: "}{fields}
                            </span>
                        </div>
                    }

                    // Description preview
                    <p class="text-sm text-gray-500 mb-3 line-clamp-2">
                        {&product.description}
                    </p>

                    // Price and availability
                    <div class="flex items-center justify-between">
                        <div class="text-lg font-bold text-primary-600">
                            {price_display}
                        </div>

                        <div class="flex items-center space-x-1">
                            <Icon icon_type={IconType::Calendar} class="h-4 w-4 text-gray-400" />
                            <span class="text-xs text-gray-500">{"Có sẵn"}</span>
                        </div>
                    </div>

                    // Tags (if any)
                    if !product.tags.is_empty() {
                        <div class="mt-3 flex flex-wrap gap-1">
                            {for product.tags.iter().take(3).map(|tag| {
                                html! {
                                    <span key={tag.clone()} class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                        {tag}
                                    </span>
                                }
                            })}
                            if product.tags.len() > 3 {
                                <span class="text-xs text-gray-400">
                                    {format!("+{}", product.tags.len() - 3)}
                                </span>
                            }
                        </div>
                    }
                </div>
            </Card>
        </Link<Route>>
    }
}
