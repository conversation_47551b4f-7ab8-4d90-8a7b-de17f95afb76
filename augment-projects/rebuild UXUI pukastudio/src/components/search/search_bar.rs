use crate::components::search::SearchSuggestions;
use crate::components::ui::{Icon, IconType};
use crate::services::mock_data::load_products_from_json;
use crate::services::search::SearchService;
use crate::types::*;
use crate::Route;
use gloo_timers::callback::Timeout;
use web_sys::HtmlInputElement;
use yew::prelude::*;
use yew_router::prelude::*;

#[derive(Properties, PartialEq)]
pub struct SearchBarProps {
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub placeholder: String,
    #[prop_or(false)]
    pub show_suggestions: bool,
    #[prop_or_default]
    pub on_search: Option<Callback<String>>,
    #[prop_or(false)]
    pub auto_focus: bool,
    #[prop_or_default]
    pub on_focus_handled: Option<Callback<()>>,
}

#[function_component(SearchBar)]
pub fn search_bar(props: &SearchBarProps) -> Html {
    let navigator = use_navigator().unwrap();
    let search_query = use_state(|| String::new());
    let suggestions = use_state(|| Vec::<SearchSuggestion>::new());
    let show_suggestions = use_state(|| false);
    let search_input_ref = use_node_ref();
    let debounce_timeout = use_state(|| None::<Timeout>);
    let products = use_state(|| Vec::<Product>::new());
    let search_service = use_state(|| SearchService::new());
    let selected_suggestion_index = use_state(|| -1i32);
    let is_typing = use_state(|| false);

    // Load products on component mount
    {
        let products = products.clone();
        use_effect_with((), move |_| {
            let products = products.clone();
            wasm_bindgen_futures::spawn_local(async move {
                match load_products_from_json().await {
                    Ok(loaded_products) => {
                        products.set(loaded_products);
                    }
                    Err(e) => {
                        web_sys::console::error_1(
                            &format!("Failed to load products: {}", e).into(),
                        );
                    }
                }
            });
            || ()
        });
    }

    // Auto-focus effect when auto_focus prop changes
    {
        let search_input_ref = search_input_ref.clone();
        let on_focus_handled = props.on_focus_handled.clone();
        let auto_focus = props.auto_focus;

        use_effect_with(auto_focus, move |&should_focus| {
            if should_focus {
                if let Some(input) = search_input_ref.cast::<HtmlInputElement>() {
                    let _ = input.focus();
                    if let Some(callback) = on_focus_handled {
                        callback.emit(());
                    }
                }
            }
            || ()
        });
    }

    let placeholder_text = if props.placeholder.is_empty() {
        "Tìm kiếm trang phục, nhân vật...".to_string()
    } else {
        props.placeholder.clone()
    };

    let on_input = {
        let search_query = search_query.clone();
        let suggestions = suggestions.clone();
        let show_suggestions = show_suggestions.clone();
        let debounce_timeout = debounce_timeout.clone();
        let products = products.clone();
        let search_service = search_service.clone();
        let selected_suggestion_index = selected_suggestion_index.clone();
        let is_typing = is_typing.clone();
        let show_suggestions_prop = props.show_suggestions;

        Callback::from(move |e: InputEvent| {
            let input: HtmlInputElement = e.target_unchecked_into();
            let value = input.value();
            search_query.set(value.clone());
            is_typing.set(true);
            selected_suggestion_index.set(-1); // Reset selection when typing

            // Clear existing timeout
            if let Some(timeout) = (*debounce_timeout).as_ref() {
                // We can't cancel the timeout directly, so we'll just let it complete
                // The timeout will check if the query is still the same
            }

            if show_suggestions_prop && !value.trim().is_empty() {
                // Debounce suggestions
                let suggestions = suggestions.clone();
                let show_suggestions = show_suggestions.clone();
                let products = products.clone();
                let search_service = search_service.clone();
                let is_typing = is_typing.clone();
                let query = value.clone();

                let timeout = Timeout::new(300, move || {
                    let search_suggestions = search_service.get_suggestions(&query, &products);
                    suggestions.set(search_suggestions);
                    show_suggestions.set(true);
                    is_typing.set(false); // Done typing/processing
                });

                debounce_timeout.set(Some(timeout));
            } else {
                show_suggestions.set(false);
                is_typing.set(false);
            }
        })
    };

    let on_keydown = {
        let search_query = search_query.clone();
        let show_suggestions = show_suggestions.clone();
        let suggestions = suggestions.clone();
        let selected_suggestion_index = selected_suggestion_index.clone();
        let navigator = navigator.clone();
        let on_search = props.on_search.clone();

        Callback::from(move |e: KeyboardEvent| {
            match e.key().as_str() {
                "Enter" => {
                    e.prevent_default();

                    // If a suggestion is selected, use it
                    if *selected_suggestion_index >= 0
                        && (*selected_suggestion_index as usize) < suggestions.len()
                    {
                        let selected_suggestion = &suggestions[*selected_suggestion_index as usize];
                        let query = selected_suggestion.text.clone();
                        search_query.set(query.clone());
                        show_suggestions.set(false);
                        let _ = navigator.push(&Route::Search { query });
                    } else {
                        // Use current query
                        let query = (*search_query).clone();
                        if !query.trim().is_empty() {
                            show_suggestions.set(false);

                            if let Some(ref callback) = on_search {
                                callback.emit(query.clone());
                            } else {
                                // Navigate to search results page
                                let _ = navigator.push(&Route::Search { query });
                            }
                        }
                    }
                }
                "ArrowDown" => {
                    e.prevent_default();
                    if !suggestions.is_empty() && *show_suggestions {
                        let new_index =
                            if *selected_suggestion_index < (suggestions.len() as i32 - 1) {
                                *selected_suggestion_index + 1
                            } else {
                                0 // Wrap to first
                            };
                        selected_suggestion_index.set(new_index);
                    }
                }
                "ArrowUp" => {
                    e.prevent_default();
                    if !suggestions.is_empty() && *show_suggestions {
                        let new_index = if *selected_suggestion_index > 0 {
                            *selected_suggestion_index - 1
                        } else {
                            suggestions.len() as i32 - 1 // Wrap to last
                        };
                        selected_suggestion_index.set(new_index);
                    }
                }
                "Escape" => {
                    show_suggestions.set(false);
                    selected_suggestion_index.set(-1);
                }
                _ => {}
            }
        })
    };

    let on_search_click = {
        let search_query = search_query.clone();
        let show_suggestions = show_suggestions.clone();
        let navigator = navigator.clone();
        let on_search = props.on_search.clone();
        let search_input_ref = search_input_ref.clone();

        Callback::from(move |_: MouseEvent| {
            let query = (*search_query).clone();

            if !query.trim().is_empty() {
                show_suggestions.set(false);

                if let Some(ref callback) = on_search {
                    callback.emit(query.clone());
                } else {
                    // Navigate to search results page
                    let _ = navigator.push(&Route::Search { query });
                }
            } else {
                // If no query, focus the input to encourage typing
                if let Some(input) = search_input_ref.cast::<HtmlInputElement>() {
                    let _ = input.focus();
                }
            }
        })
    };

    let on_focus = {
        let show_suggestions = show_suggestions.clone();
        let search_query = search_query.clone();
        let show_suggestions_prop = props.show_suggestions;

        Callback::from(move |_: FocusEvent| {
            if show_suggestions_prop && !(*search_query).trim().is_empty() {
                show_suggestions.set(true);
            }
        })
    };

    let on_blur = {
        let show_suggestions = show_suggestions.clone();

        Callback::from(move |_: FocusEvent| {
            // Delay hiding suggestions to allow for clicks
            let show_suggestions = show_suggestions.clone();
            Timeout::new(150, move || {
                show_suggestions.set(false);
            })
            .forget();
        })
    };

    let on_suggestion_select = {
        let search_query = search_query.clone();
        let show_suggestions = show_suggestions.clone();
        let navigator = navigator.clone();
        let search_input_ref = search_input_ref.clone();

        Callback::from(move |suggestion: SearchSuggestion| {
            search_query.set(suggestion.text.clone());
            show_suggestions.set(false);

            // Focus back to input
            if let Some(input) = search_input_ref.cast::<HtmlInputElement>() {
                let _ = input.focus();
            }

            // Navigate to search results
            let _ = navigator.push(&Route::Search {
                query: suggestion.text,
            });
        })
    };

    let container_class = classes!("relative", props.class.clone());

    // Determine input styling based on state
    let input_class = classes!(
        "w-full",
        "pl-10",
        "pr-12",
        "py-2",
        "border",
        "rounded-lg",
        "bg-white",
        "dark:bg-gray-800",
        "text-gray-900",
        "dark:text-gray-100",
        "focus:ring-2",
        "focus:ring-primary-500",
        "focus:border-primary-500",
        "transition-all",
        "duration-200",
        if *is_typing {
            "border-primary-300 dark:border-primary-600 bg-primary-50 dark:bg-primary-900/20"
        } else {
            "border-gray-300 dark:border-gray-600"
        }
    );

    // Determine search button styling
    let search_button_class = classes!(
        "absolute",
        "right-2",
        "top-1/2",
        "transform",
        "-translate-y-1/2",
        "p-1",
        "transition-all",
        "duration-200",
        "rounded",
        if (*search_query).trim().is_empty() {
            "text-gray-400 dark:text-gray-500 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20"
        } else {
            "text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-900/30"
        }
    );

    html! {
        <div class={container_class}>
            <div class="relative">
                <input
                    ref={search_input_ref}
                    type="text"
                    value={(*search_query).clone()}
                    placeholder={placeholder_text}
                    class={input_class}
                    oninput={on_input}
                    onkeydown={on_keydown}
                    onfocus={on_focus}
                    onblur={on_blur}
                />

                // Search icon with typing indicator
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                    if *is_typing {
                        <div class="animate-spin h-4 w-4 border-2 border-primary-600 dark:border-primary-400 border-t-transparent rounded-full"></div>
                    } else {
                        <Icon
                            icon_type={IconType::Search}
                            class="h-4 w-4 text-gray-400 dark:text-gray-500"
                        />
                    }
                </div>

                // Enhanced search button
                <button
                    onclick={on_search_click}
                    class={search_button_class}
                    aria-label={
                        if (*search_query).trim().is_empty() {
                            "Click to start typing"
                        } else {
                            "Search"
                        }
                    }
                    title={
                        if (*search_query).trim().is_empty() {
                            "Click to focus search input"
                        } else {
                            "Search for products"
                        }
                    }
                >
                    if (*search_query).trim().is_empty() {
                        <Icon icon_type={IconType::Search} class="h-4 w-4" />
                    } else {
                        <Icon icon_type={IconType::ArrowRight} class="h-4 w-4" />
                    }
                </button>
            </div>

            // Search suggestions dropdown with keyboard navigation
            if props.show_suggestions && *show_suggestions && !(*suggestions).is_empty() {
                <SearchSuggestions
                    suggestions={(*suggestions).clone()}
                    selected_index={*selected_suggestion_index}
                    on_select={on_suggestion_select}
                />
            }

            // Typing indicator
            if *is_typing && (*search_query).trim().len() > 0 {
                <div class="absolute top-full left-0 right-0 mt-1 px-3 py-2 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg text-sm text-primary-700 dark:text-primary-300">
                    <div class="flex items-center space-x-2">
                        <div class="animate-pulse h-2 w-2 bg-primary-500 dark:bg-primary-400 rounded-full"></div>
                        <span>{"Đang tìm kiếm..."}</span>
                    </div>
                </div>
            }
        </div>
    }
}
