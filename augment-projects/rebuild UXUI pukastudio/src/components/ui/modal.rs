use crate::components::ui::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Icon, IconType};
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct ModalProps {
    pub children: Children,
    pub open: bool,
    pub onclose: Callback<()>,
    #[prop_or_default]
    pub title: Option<AttrValue>,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(Modal)]
pub fn modal(props: &ModalProps) -> Html {
    let backdrop_onclick = {
        let onclose = props.onclose.clone();
        Callback::from(move |e: MouseEvent| {
            e.prevent_default();
            onclose.emit(());
        })
    };

    let modal_onclick = Callback::from(|e: MouseEvent| {
        e.stop_propagation();
    });

    if !props.open {
        return html! {};
    }

    html! {
        <div class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                // Backdrop
                <div
                    class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-80"
                    onclick={backdrop_onclick}
                ></div>

                // Modal
                <div
                    class={classes!(
                        "inline-block",
                        "w-full",
                        "max-w-md",
                        "p-6",
                        "my-8",
                        "overflow-hidden",
                        "text-left",
                        "align-middle",
                        "transition-all",
                        "transform",
                        "bg-white",
                        "dark:bg-gray-800",
                        "shadow-xl",
                        "rounded-lg",
                        "animate-scale-in",
                        props.class.clone()
                    )}
                    onclick={modal_onclick}
                >
                    // Header
                    if props.title.is_some() {
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                { props.title.as_ref().unwrap() }
                            </h3>
                            <button
                                onclick={props.onclose.reform(|_| ())}
                                class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                            >
                                <Icon icon_type={IconType::X} class="h-5 w-5" />
                            </button>
                        </div>
                    }

                    // Content
                    { for props.children.iter() }
                </div>
            </div>
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct ConfirmModalProps {
    pub open: bool,
    pub onconfirm: Callback<()>,
    pub oncancel: Callback<()>,
    #[prop_or("Confirm".to_string())]
    pub title: String,
    #[prop_or("Are you sure?".to_string())]
    pub message: String,
    #[prop_or("Confirm".to_string())]
    pub confirm_text: String,
    #[prop_or("Cancel".to_string())]
    pub cancel_text: String,
}

#[function_component(ConfirmModal)]
pub fn confirm_modal(props: &ConfirmModalProps) -> Html {
    html! {
        <Modal
            open={props.open}
            onclose={props.oncancel.reform(|_| ())}
            title={props.title.clone()}
        >
            <div class="mb-6">
                <p class="text-gray-600 dark:text-gray-300">{ &props.message }</p>
            </div>

            <div class="flex justify-end space-x-3">
                <Button
                    variant={ButtonVariant::Ghost}
                    onclick={props.oncancel.reform(|_| ())}
                >
                    { props.cancel_text.clone() }
                </Button>
                <Button
                    variant={ButtonVariant::Danger}
                    onclick={props.onconfirm.reform(|_| ())}
                >
                    { props.confirm_text.clone() }
                </Button>
            </div>
        </Modal>
    }
}
