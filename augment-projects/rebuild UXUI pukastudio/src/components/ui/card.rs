use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct CardProps {
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or(false)]
    pub hover: bool,
    #[prop_or_default]
    pub onclick: Callback<MouseEvent>,
}

#[function_component(Card)]
pub fn card(props: &CardProps) -> Html {
    let class = classes!(
        "card",
        props.hover.then_some("card-anime"),
        props.class.clone()
    );

    html! {
        <div class={class} onclick={props.onclick.clone()}>
            { for props.children.iter() }
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct CardHeaderProps {
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(CardHeader)]
pub fn card_header(props: &CardHeaderProps) -> Html {
    let class = classes!(
        "px-6",
        "py-4",
        "border-b",
        "border-gray-200",
        "dark:border-gray-700",
        props.class.clone()
    );

    html! {
        <div class={class}>
            { for props.children.iter() }
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct CardBodyProps {
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(CardBody)]
pub fn card_body(props: &CardBodyProps) -> Html {
    let class = classes!("px-6", "py-4", props.class.clone());

    html! {
        <div class={class}>
            { for props.children.iter() }
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct CardFooterProps {
    pub children: Children,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(CardFooter)]
pub fn card_footer(props: &CardFooterProps) -> Html {
    let class = classes!(
        "px-6",
        "py-4",
        "border-t",
        "border-gray-200",
        "dark:border-gray-700",
        "bg-gray-50",
        "dark:bg-gray-800",
        props.class.clone()
    );

    html! {
        <div class={class}>
            { for props.children.iter() }
        </div>
    }
}
