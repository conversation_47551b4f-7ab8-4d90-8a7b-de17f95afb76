use crate::components::ui::{Icon, IconType};
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct LoadingSpinnerProps {
    #[prop_or_default]
    pub class: Classes,
    #[prop_or(24)]
    pub size: u32,
}

#[function_component(LoadingSpinner)]
pub fn loading_spinner(props: &LoadingSpinnerProps) -> Html {
    let class = classes!(
        "spinner",
        format!("h-{} w-{}", props.size / 4, props.size / 4),
        props.class.clone()
    );

    html! {
        <div class={class}></div>
    }
}

#[derive(Properties, PartialEq)]
pub struct LoadingOverlayProps {
    #[prop_or_default]
    pub message: Option<AttrValue>,
    #[prop_or_default]
    pub class: Classes,
}

#[function_component(LoadingOverlay)]
pub fn loading_overlay(props: &LoadingOverlayProps) -> Html {
    let class = classes!(
        "fixed",
        "inset-0",
        "bg-white",
        "dark:bg-gray-900",
        "bg-opacity-75",
        "dark:bg-opacity-75",
        "flex",
        "items-center",
        "justify-center",
        "z-50",
        props.class.clone()
    );

    html! {
        <div class={class}>
            <div class="text-center">
                <LoadingSpinner size={48} class="mx-auto mb-4" />
                if let Some(message) = &props.message {
                    <p class="text-gray-600 dark:text-gray-300">{ message }</p>
                }
            </div>
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct SkeletonProps {
    #[prop_or_default]
    pub class: Classes,
    #[prop_or("h-4".to_string())]
    pub height: String,
    #[prop_or("w-full".to_string())]
    pub width: String,
}

#[function_component(Skeleton)]
pub fn skeleton(props: &SkeletonProps) -> Html {
    let class = classes!(
        "shimmer",
        "rounded",
        props.height.clone(),
        props.width.clone(),
        props.class.clone()
    );

    html! {
        <div class={class}></div>
    }
}

#[derive(Properties, PartialEq)]
pub struct LoadingStateProps {
    pub loading: bool,
    pub children: Children,
    #[prop_or_default]
    pub skeleton: Option<Html>,
}

#[function_component(LoadingState)]
pub fn loading_state(props: &LoadingStateProps) -> Html {
    if props.loading {
        if let Some(skeleton) = &props.skeleton {
            skeleton.clone()
        } else {
            html! {
                <div class="animate-pulse">
                    <Skeleton height="h-6" width="w-3/4" class="mb-2" />
                    <Skeleton height="h-4" width="w-1/2" class="mb-2" />
                    <Skeleton height="h-4" width="w-full" />
                </div>
            }
        }
    } else {
        html! {
            <>{ for props.children.iter() }</>
        }
    }
}
