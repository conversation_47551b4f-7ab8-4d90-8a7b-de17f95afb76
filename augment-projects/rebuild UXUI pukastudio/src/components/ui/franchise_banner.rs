use crate::components::ui::{Icon, IconType};
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct FranchiseBannerProps {
    pub franchise_name: String,
    pub category_type: String,
    #[prop_or_default]
    pub class: Classes,
    #[prop_or_default]
    pub children: Children,
}

#[function_component(FranchiseBanner)]
pub fn franchise_banner(props: &FranchiseBannerProps) -> Html {
    let image_loading = use_state(|| true);
    let image_error = use_state(|| false);
    let current_src = use_state(|| get_franchise_image_url(&props.franchise_name));

    // Helper function to get franchise-specific image URL
    let get_franchise_image_url = |franchise_name: &str| -> String {
        // Log the franchise name for debugging
        web_sys::console::log_1(
            &format!("🖼️ Looking for image for franchise: '{}'", franchise_name).into(),
        );

        // Map specific franchises to their correct banner images based on original website
        let image_url = match franchise_name.to_lowercase().as_str() {
            // Correct mappings based on found_images.json from original website
            "one piece" => "/images/franchises/OIP.jpg".to_string(), // ✅ Correct - One Piece logo
            "attack on titan" => {
                "/images/franchises/460846942_1260573491568410_7549415256311422204_n-1024x773.jpg"
                    .to_string()
            } // ✅ Correct - Attack on Titan image
            "vocaloid" | "cantarella" => "/images/franchises/Cantarella-300x251.png".to_string(), // ✅ Correct - Vocaloid/Cantarella image
            "genshin impact" => "/images/franchises/675bdc6e866922377b78-768x1024.jpg".to_string(), // ✅ Correct - Genshin Impact image
            "honkai star rail" | "honkai: star rail" => {
                "/images/franchises/7c7a187b427ce622bf6d-682x1024.jpg".to_string()
            } // ✅ Correct - Honkai Star Rail image
            "frieren" | "frieren: beyond journey's end" => {
                "/images/franchises/1d1ab505ee024a5c1313-676x1024.jpg".to_string()
            } // ✅ Correct - Frieren image

            // For franchises without specific images, use unique images to avoid duplicates in featured section
            "league of legends" => "/images/franchises/Cantarella-300x251.png".to_string(), // Use Cantarella for LoL (unique assignment)
            "nier: automata" => "/images/franchises/Cantarella-300x251.png".to_string(), // Use Cantarella for Nier (unique assignment)
            "spy x family" => "/images/franchises/Cantarella-1-300x251.png".to_string(), // Use alternative Cantarella for Spy x Family
            "zenless zone zero" => "/images/franchises/Cantarella-300x251.png".to_string(), // Use Cantarella for ZZZ
            _ => {
                // Default fallback - will trigger error and show icon instead
                format!(
                    "/images/franchises/{}.jpg",
                    franchise_name.to_lowercase().replace(" ", "-")
                )
            }
        };

        web_sys::console::log_1(&format!("🖼️ Using image URL: '{}'", image_url).into());
        image_url
    };

    // Helper function to get fallback icon and gradient based on category type
    let get_fallback_style = |category_type: &str| -> (IconType, &'static str) {
        match category_type {
            "game" => (
                IconType::GameController,
                "bg-gradient-to-br from-blue-500 to-purple-600",
            ),
            "anime" => (IconType::Star, "bg-gradient-to-br from-pink-500 to-red-500"),
            "vtuber" => (
                IconType::Video,
                "bg-gradient-to-br from-green-500 to-teal-500",
            ),
            "vocaloid" => (
                IconType::Music,
                "bg-gradient-to-br from-yellow-500 to-orange-500",
            ),
            _ => (
                IconType::Star,
                "bg-gradient-to-br from-gray-500 to-gray-600",
            ),
        }
    };

    let on_image_load = {
        let image_loading = image_loading.clone();
        let image_error = image_error.clone();
        let franchise_name = props.franchise_name.clone();
        Callback::from(move |_: Event| {
            web_sys::console::log_1(
                &format!(
                    "✅ Successfully loaded image for franchise: '{}'",
                    franchise_name
                )
                .into(),
            );
            image_loading.set(false);
            image_error.set(false);
        })
    };

    let on_image_error = {
        let image_error = image_error.clone();
        let image_loading = image_loading.clone();
        let current_src = current_src.clone();
        let franchise_name = props.franchise_name.clone();

        Callback::from(move |_: Event| {
            web_sys::console::error_1(
                &format!(
                    "❌ Failed to load image for franchise: '{}', current src: '{}'",
                    franchise_name, *current_src
                )
                .into(),
            );

            if !*image_error {
                image_error.set(true);
                image_loading.set(false);

                // Try alternative image paths
                let alt_url = format!(
                    "/images/franchises/{}.png",
                    franchise_name.to_lowercase().replace(" ", "-")
                );
                web_sys::console::log_1(
                    &format!("🔄 Trying alternative image URL: '{}'", alt_url).into(),
                );
                current_src.set(alt_url);
            } else {
                web_sys::console::error_1(&format!("❌ Alternative image also failed for franchise: '{}', falling back to icon", franchise_name).into());
            }
        })
    };

    let (fallback_icon, fallback_gradient) = get_fallback_style(&props.category_type);

    html! {
        <div class={classes!("relative", "h-32", "overflow-hidden", "rounded-t-lg", props.class.clone())}>
            if *image_error {
                // Fallback to gradient with icon
                <div class={classes!("h-full", "w-full", "flex", "items-center", "justify-center", "relative", fallback_gradient)}>
                    <Icon icon_type={fallback_icon} class="h-12 w-12 text-white" />
                    { for props.children.iter() }
                </div>
            } else {
                // Show image with overlay
                <>
                    if *image_loading {
                        // Loading shimmer effect
                        <div class="h-full w-full bg-gray-200 dark:bg-gray-700 animate-pulse flex items-center justify-center">
                            <div class="text-gray-400 dark:text-gray-500 text-sm">{"Loading..."}</div>
                        </div>
                    }

                    <img
                        src={(*current_src).clone()}
                        alt={format!("{} banner", props.franchise_name)}
                        class={classes!(
                            "w-full", "h-full", "object-cover", "object-center",
                            if *image_loading { "opacity-0" } else { "opacity-100" },
                            "transition-opacity", "duration-300"
                        )}
                        onload={on_image_load}
                        onerror={on_image_error}
                        loading="lazy"
                    />

                    // Content overlay (removed dark overlay to match original website)
                    <div class="absolute inset-0 flex items-center justify-center">
                        { for props.children.iter() }
                    </div>
                </>
            }
        </div>
    }
}

// Helper function to get franchise image URL (can be used externally)
pub fn get_franchise_image_url(franchise_name: &str) -> String {
    match franchise_name.to_lowercase().as_str() {
        // Correct mappings based on found_images.json from original website
        "one piece" => "/images/franchises/OIP.jpg".to_string(), // ✅ Correct - One Piece logo
        "attack on titan" => {
            "/images/franchises/460846942_1260573491568410_7549415256311422204_n-1024x773.jpg"
                .to_string()
        } // ✅ Correct - Attack on Titan image
        "vocaloid" | "cantarella" => "/images/franchises/Cantarella-300x251.png".to_string(), // ✅ Correct - Vocaloid/Cantarella image
        "genshin impact" => "/images/franchises/675bdc6e866922377b78-768x1024.jpg".to_string(), // ✅ Correct - Genshin Impact image
        "honkai star rail" | "honkai: star rail" => {
            "/images/franchises/7c7a187b427ce622bf6d-682x1024.jpg".to_string()
        } // ✅ Correct - Honkai Star Rail image
        "frieren" | "frieren: beyond journey's end" => {
            "/images/franchises/1d1ab505ee024a5c1313-676x1024.jpg".to_string()
        } // ✅ Correct - Frieren image

        // For franchises without specific images, use unique images to avoid duplicates in featured section
        "league of legends" => "/images/franchises/Cantarella-300x251.png".to_string(), // Use Cantarella for LoL (unique assignment)
        "nier: automata" => "/images/franchises/Cantarella-300x251.png".to_string(), // Use Cantarella for Nier (unique assignment)
        "spy x family" => "/images/franchises/Cantarella-1-300x251.png".to_string(), // Use alternative Cantarella for Spy x Family
        "zenless zone zero" => "/images/franchises/Cantarella-300x251.png".to_string(), // Use Cantarella for ZZZ
        _ => format!(
            "/images/franchises/{}.jpg",
            franchise_name.to_lowercase().replace(" ", "-")
        ),
    }
}
