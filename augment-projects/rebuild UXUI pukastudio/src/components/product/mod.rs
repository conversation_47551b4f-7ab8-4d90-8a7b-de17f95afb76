use crate::components::layout::{Breadcrumb, BreadcrumbItem};
use crate::types::{Product, RentalOption, ShoeOption, WeaponAccessory};
use crate::utils::format::format_currency;
use crate::Route;
use web_sys::MouseEvent;
use yew::prelude::*;

#[derive(Properties, PartialEq)]
pub struct ProductDetailProps {
    pub product: Product,
}

#[function_component(ProductDetail)]
pub fn product_detail(props: &ProductDetailProps) -> Html {
    let product = &props.product;
    let selected_image = use_state(|| 0usize);
    let selected_rental = use_state(|| None::<usize>);

    // Simplified breadcrumb - only essential hierarchy
    let breadcrumb_items = vec![
        BreadcrumbItem {
            label: product.franchise.clone(),
            route: Some(Route::CatalogCategory {
                category: product.category.as_str().to_string(),
            }),
        },
        BreadcrumbItem {
            label: product.character_name.clone(),
            route: None,
        },
    ];

    html! {
        <div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
            // Clean, minimal breadcrumb navigation
            <Breadcrumb items={breadcrumb_items} />

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ultra-compact">
                <div class="lg:grid lg:grid-cols-2 lg:gap-x-12 lg:items-start">
                    // Enhanced Product Images with Gallery
                    <div class="flex flex-col">
                        // Main Image with Flexible Height
                        <div class="w-full mb-3">
                            <div class="relative group shadow-xl rounded-2xl transition-all duration-300 group-hover:shadow-2xl overflow-hidden dark:shadow-gray-800">
                                <div class="image-container">
                                    <img
                                        src={product.images.get(*selected_image).unwrap_or(&"/images/placeholder.jpg".to_string()).clone()}
                                        alt={product.name.clone()}
                                        class="transition-transform duration-300 group-hover:scale-105"
                                    />
                                </div>
                            </div>
                        </div>

                        // Thumbnail Gallery
                        <div class="grid grid-cols-4 gap-4">
                            {for product.images.iter().take(4).enumerate().map(|(i, image)| {
                                let selected_image = selected_image.clone();
                                let is_selected = *selected_image == i;
                                html! {
                                    <div
                                        key={i}
                                        class={format!("w-full aspect-square rounded-lg overflow-hidden cursor-pointer transition-all duration-200 bg-gray-50 dark:bg-gray-700 flex items-center justify-center {}",
                                            if is_selected { "ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900" } else { "hover:opacity-75" }
                                        )}
                                        onclick={
                                            let selected_image = selected_image.clone();
                                            Callback::from(move |_| selected_image.set(i))
                                        }
                                    >
                                        <img
                                            src={image.clone()}
                                            alt={format!("{} - Image {}", product.name, i + 1)}
                                            class="max-w-full max-h-full object-contain"
                                        />
                                    </div>
                                }
                            })}
                        </div>
                    </div>

                // Enhanced Product Info
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-anime p-4 lg:mt-0 mt-3 border border-gray-100 dark:border-gray-700 transition-colors duration-200">
                    // Enhanced Product Header
                    <div class="border-b border-gray-200 dark:border-gray-600 pb-8">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                // Category badge for context (non-redundant)
                                <div class="mb-3">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-primary-100 dark:bg-primary-800 text-primary-800 dark:text-primary-200 transition-colors duration-200">
                                        {&product.category.display_name()}
                                    </span>
                                </div>
                                <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-3 leading-tight transition-colors duration-200">
                                    {&product.name}
                                </h1>
                                <div class="flex items-center space-x-3 mb-4">
                                    <h2 class="text-xl sm:text-2xl text-gray-600 dark:text-gray-300 font-medium transition-colors duration-200">
                                        {&product.character_name}
                                    </h2>
                                    {if product.featured {
                                        html! {
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-md animate-pulse">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                                {"Nổi bật"}
                                            </span>
                                        }
                                    } else {
                                        html! { <></> }
                                    }}
                                </div>
                            </div>
                        </div>

                        // Franchise and Category Tags
                        <div class="flex flex-wrap items-center gap-3 mb-4">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {&product.franchise}
                            </span>
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-green-500 to-teal-600 text-white shadow-md">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
                                </svg>
                                {product.category.display_name()}
                            </span>
                            {if product.featured {
                                html! {
                                    <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-md">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        {"Nổi bật"}
                                    </span>
                                }
                            } else {
                                html! { <></> }
                            }}
                        </div>

                        // Price Range
                        {if let Some(price_range) = &product.price_range {
                            html! {
                                <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-4 border border-purple-200 dark:border-purple-700 transition-colors duration-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-purple-800 dark:text-purple-300 transition-colors duration-200">{"Khoảng giá thuê"}</p>
                                            <p class="text-2xl font-bold text-purple-900 dark:text-purple-100 transition-colors duration-200">
                                                {format_currency(price_range.min)} {" - "} {format_currency(price_range.max)}
                                            </p>
                                        </div>
                                        <div class="text-purple-600 dark:text-purple-400 transition-colors duration-200">
                                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            }
                        } else {
                            html! { <></> }
                        }}
                    </div>

                    // Description
                    <div class="mt-6">
                        <h3 class="sr-only">{"Description"}</h3>
                        <div class="text-base text-gray-700 dark:text-gray-300 space-y-6 transition-colors duration-200">
                            {for product.description.split('\n').map(|line| {
                                html! { <p>{line}</p> }
                            })}
                        </div>
                    </div>

                    // Rental Rules
                    {if let Some(rental_rules) = &product.rental_rules {
                        html! {
                            <div class="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg transition-colors duration-200">
                                <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-300 transition-colors duration-200">{"Quy định thuê"}</h4>
                                <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-200 transition-colors duration-200">{rental_rules}</p>
                            </div>
                        }
                    } else {
                        html! { <></> }
                    }}

                    // Contact Info
                    {if let Some(contact_info) = &product.contact_info {
                        html! {
                            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg transition-colors duration-200">
                                <h4 class="text-sm font-medium text-blue-800 dark:text-blue-300 transition-colors duration-200">{"Liên hệ"}</h4>
                                <p class="mt-1 text-sm text-blue-700 dark:text-blue-200 transition-colors duration-200">{contact_info}</p>
                            </div>
                        }
                    } else {
                        html! { <></> }
                    }}

                    // Enhanced Sizes Section
                    <div class="mt-8">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center transition-colors duration-200">
                            <svg class="w-5 h-5 mr-2 text-purple-600 dark:text-purple-400 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                            {"Kích thước có sẵn"}
                        </h3>
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                            {for product.sizes.iter().map(|size| {
                                html! {
                                    <div class="relative group">
                                        <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-2 border-purple-200 dark:border-purple-700 rounded-xl p-4 text-center transition-all duration-200 hover:border-purple-400 dark:hover:border-purple-500 hover:shadow-md cursor-pointer">
                                            <div class="text-lg font-bold text-purple-900 dark:text-purple-100 mb-1 transition-colors duration-200">
                                                {size.as_str()}
                                            </div>
                                            <div class="text-xs text-purple-600 dark:text-purple-400 font-medium transition-colors duration-200">
                                                {"Size"}
                                            </div>
                                        </div>
                                        <div class="absolute inset-0 bg-purple-500 opacity-0 group-hover:opacity-5 dark:group-hover:opacity-10 rounded-xl transition-opacity duration-200"></div>
                                    </div>
                                }
                            })}
                        </div>
                    </div>

                    // Enhanced Rental Options
                    <div class="mt-8">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center transition-colors duration-200">
                            <svg class="w-6 h-6 mr-3 text-blue-600 dark:text-blue-400 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 3a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                            </svg>
                            {"Tùy chọn thuê"}
                        </h3>
                        <div class="grid gap-4">
                            {for product.rental_options.iter().enumerate().map(|(i, option)| {
                                let selected_rental = selected_rental.clone();
                                let is_selected = *selected_rental == Some(i);
                                html! {
                                    <EnhancedRentalOptionCard
                                        rental_option={option.clone()}
                                        is_selected={is_selected}
                                        on_select={
                                            let selected_rental = selected_rental.clone();
                                            Callback::from(move |_| {
                                                if is_selected {
                                                    selected_rental.set(None);
                                                } else {
                                                    selected_rental.set(Some(i));
                                                }
                                            })
                                        }
                                    />
                                }
                            })}
                        </div>

                        // Contact CTA
                        <div class="mt-8 bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-700 dark:to-purple-700 rounded-2xl p-6 text-white transition-colors duration-200">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                <div class="flex-1 min-w-0">
                                    <h4 class="text-lg sm:text-xl font-bold mb-2 leading-tight">{"Sẵn sàng thuê costume?"}</h4>
                                    <p class="text-blue-100 dark:text-blue-200 text-sm sm:text-base leading-relaxed transition-colors duration-200">{"Liên hệ ngay để được tư vấn và đặt lịch"}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <button class="w-full sm:w-auto bg-white dark:bg-gray-100 text-blue-600 dark:text-blue-700 px-6 py-3 rounded-xl font-semibold hover:bg-blue-50 dark:hover:bg-gray-200 transition-colors duration-200 shadow-lg text-sm sm:text-base">
                                        {"Liên hệ ngay"}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    // Enhanced Accessories Section
                    {if !product.weapon_accessories.is_empty() || !product.shoe_options.is_empty() {
                        html! {
                            <div class="mt-8">
                                <h3 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center leading-tight transition-colors duration-200">
                                    <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-purple-600 dark:text-purple-400 flex-shrink-0 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                    </svg>
                                    <span class="break-words">{"Phụ kiện & Tùy chọn"}</span>
                                </h3>

                                <div class="grid md:grid-cols-2 gap-6">
                                    // Weapon Accessories
                                    {if !product.weapon_accessories.is_empty() {
                                        html! {
                                            <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-4 sm:p-6 border border-purple-200 dark:border-purple-700 transition-colors duration-200">
                                                <h4 class="text-base sm:text-lg font-semibold text-purple-900 dark:text-purple-100 mb-4 flex items-center leading-tight transition-colors duration-200">
                                                    <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 flex-shrink-0 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                    </svg>
                                                    <span class="break-words">{"Vũ khí & Phụ kiện"}</span>
                                                </h4>
                                                <div class="space-y-3">
                                                    {for product.weapon_accessories.iter().map(|weapon| {
                                                        html! { <EnhancedWeaponAccessoryCard weapon={weapon.clone()} /> }
                                                    })}
                                                </div>
                                            </div>
                                        }
                                    } else {
                                        html! { <></> }
                                    }}

                                    // Shoe Options
                                    {if !product.shoe_options.is_empty() {
                                        html! {
                                            <div class="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-4 sm:p-6 border border-green-200 dark:border-green-700 transition-colors duration-200">
                                                <h4 class="text-base sm:text-lg font-semibold text-green-900 dark:text-green-100 mb-4 flex items-center leading-tight transition-colors duration-200">
                                                    <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 flex-shrink-0 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"/>
                                                        <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"/>
                                                    </svg>
                                                    <span class="break-words">{"Tùy chọn giày"}</span>
                                                </h4>
                                                <div class="space-y-3">
                                                    {for product.shoe_options.iter().map(|shoe| {
                                                        html! { <EnhancedShoeOptionCard shoe={shoe.clone()} /> }
                                                    })}
                                                </div>
                                            </div>
                                        }
                                    } else {
                                        html! { <></> }
                                    }}
                                </div>
                            </div>
                        }
                    } else {
                        html! { <></> }
                    }}

                    // Product Information
                    <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600 transition-colors duration-200">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200">{"Mã sản phẩm:"}</span>
                                <span class="ml-2 text-gray-600 dark:text-gray-300 transition-colors duration-200">
                                    {if let Some(sku) = &product.sku {
                                        if sku == "N/A" {
                                            format!("PSK-{}", product.id.to_uppercase().chars().take(8).collect::<String>())
                                        } else {
                                            sku.clone()
                                        }
                                    } else {
                                        format!("PSK-{}", product.id.to_uppercase().chars().take(8).collect::<String>())
                                    }}
                                </span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200">{"Danh mục:"}</span>
                                <span class="ml-2 text-gray-600 dark:text-gray-300 transition-colors duration-200">{product.category.display_name()}</span>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct RentalOptionCardProps {
    pub rental_option: RentalOption,
}

#[function_component(RentalOptionCard)]
pub fn rental_option_card(props: &RentalOptionCardProps) -> Html {
    let option = &props.rental_option;

    html! {
        <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-start">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">
                        {option.rental_type.display_name()}
                    </h4>
                    <p class="mt-1 text-sm text-gray-500">
                        {&option.description}
                    </p>
                    <p class="mt-1 text-xs text-gray-400">
                        {format!("{} giờ", option.duration_hours)}
                        {if option.includes_studio { " • Bao gồm studio" } else { "" }}
                    </p>
                </div>
                <div class="text-right">
                    <p class="text-lg font-medium text-gray-900">
                        {format_currency(option.price)}
                    </p>
                </div>
            </div>
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct EnhancedRentalOptionCardProps {
    pub rental_option: RentalOption,
    pub is_selected: bool,
    pub on_select: Callback<()>,
}

#[function_component(EnhancedRentalOptionCard)]
pub fn enhanced_rental_option_card(props: &EnhancedRentalOptionCardProps) -> Html {
    let option = &props.rental_option;
    let is_selected = props.is_selected;

    let card_classes = if is_selected {
        "border-2 border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20 shadow-lg transform scale-105 transition-colors duration-200"
    } else {
        "border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-md bg-white dark:bg-gray-800 transition-colors duration-200"
    };

    let rental_type_icon = match option.rental_type.as_str() {
        "Test" => html! {
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
        },
        "PhotoShoot" => html! {
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
            </svg>
        },
        "Event" => html! {
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
            </svg>
        },
        _ => html! {
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
            </svg>
        },
    };

    html! {
        <div
            class={format!("rounded-xl p-4 sm:p-6 cursor-pointer transition-all duration-200 {}", card_classes)}
            onclick={
                let on_select = props.on_select.clone();
                Callback::from(move |_: MouseEvent| on_select.emit(()))
            }
        >
            <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                <div class="flex items-start space-x-3 sm:space-x-4 min-w-0 flex-1">
                    <div class={format!("p-2 sm:p-3 rounded-lg flex-shrink-0 transition-colors duration-200 {}",
                        if is_selected { "bg-blue-500 dark:bg-blue-600 text-white" } else { "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300" }
                    )}>
                        {rental_type_icon}
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class={format!("text-base sm:text-lg font-semibold leading-tight break-words transition-colors duration-200 {}",
                            if is_selected { "text-blue-900 dark:text-blue-100" } else { "text-gray-900 dark:text-gray-100" }
                        )}>
                            {option.rental_type.display_name()}
                        </h4>
                        <p class={format!("mt-1 text-sm leading-relaxed break-words transition-colors duration-200 {}",
                            if is_selected { "text-blue-700 dark:text-blue-200" } else { "text-gray-600 dark:text-gray-300" }
                        )}>
                            {&option.description}
                        </p>
                        <div class="mt-2 flex flex-wrap items-center gap-2 text-xs">
                            <span class={format!("inline-flex items-center px-2 py-1 rounded-full font-medium whitespace-nowrap transition-colors duration-200 {}",
                                if is_selected { "bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200" } else { "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300" }
                            )}>
                                <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                                <span class="break-words">{format!("{} giờ", option.duration_hours)}</span>
                            </span>
                            {if option.includes_studio {
                                html! {
                                    <span class={format!("inline-flex items-center px-2 py-1 rounded-full font-medium whitespace-nowrap transition-colors duration-200 {}",
                                        if is_selected { "bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200" } else { "bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300" }
                                    )}>
                                        <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                                        </svg>
                                        <span class="break-words">{"Bao gồm studio"}</span>
                                    </span>
                                }
                            } else {
                                html! { <></> }
                            }}
                        </div>
                    </div>
                </div>
                <div class="flex flex-row sm:flex-col items-center sm:items-end justify-between sm:justify-start gap-2 sm:gap-1 flex-shrink-0">
                    <p class={format!("text-xl sm:text-2xl font-bold whitespace-nowrap transition-colors duration-200 {}",
                        if is_selected { "text-blue-900 dark:text-blue-100" } else { "text-gray-900 dark:text-gray-100" }
                    )}>
                        {format_currency(option.price)}
                    </p>
                    {if is_selected {
                        html! {
                            <div class="inline-flex items-center text-blue-600 dark:text-blue-400 transition-colors duration-200">
                                <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-xs sm:text-sm font-medium whitespace-nowrap">{"Đã chọn"}</span>
                            </div>
                        }
                    } else {
                        html! {
                            <div class="text-gray-400 dark:text-gray-500 transition-colors duration-200">
                                <span class="text-xs sm:text-sm whitespace-nowrap">{"Nhấn để chọn"}</span>
                            </div>
                        }
                    }}
                </div>
            </div>
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct WeaponAccessoryCardProps {
    pub weapon: WeaponAccessory,
}

#[function_component(WeaponAccessoryCard)]
pub fn weapon_accessory_card(props: &WeaponAccessoryCardProps) -> Html {
    let weapon = &props.weapon;

    html! {
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
            <div>
                <h5 class="text-sm font-medium text-gray-900">{&weapon.name}</h5>
                <p class="text-xs text-gray-500">{&weapon.description}</p>
            </div>
            <div class="text-right">
                <p class="text-sm font-medium text-gray-900">
                    {if weapon.price > 0 {
                        format!("+{}", format_currency(weapon.price))
                    } else {
                        "Miễn phí".to_string()
                    }}
                </p>
                <p class="text-xs text-gray-500">
                    {if weapon.available { "Có sẵn" } else { "Hết hàng" }}
                </p>
            </div>
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct InteractiveWeaponAccessoryCardProps {
    pub weapon: WeaponAccessory,
    pub selected: bool,
    pub on_toggle: Callback<String>,
}

#[function_component(InteractiveWeaponAccessoryCard)]
pub fn interactive_weapon_accessory_card(props: &InteractiveWeaponAccessoryCardProps) -> Html {
    let weapon = &props.weapon;
    let selected = props.selected;
    let on_toggle = props.on_toggle.clone();

    let onclick = {
        let weapon_id = weapon.id.clone();
        let on_toggle = on_toggle.clone();
        Callback::from(move |_| {
            on_toggle.emit(weapon_id.clone());
        })
    };

    html! {
        <div
            class={format!("bg-white rounded-xl border transition-all duration-200 p-4 cursor-pointer {}",
                if selected {
                    "border-purple-500 ring-2 ring-purple-200 shadow-md"
                } else {
                    "border-purple-200 hover:border-purple-400 hover:shadow-md"
                }
            )}
            onclick={onclick}
        >
            <div class="flex items-start justify-between gap-4">
                // Left section: Icon + Content
                <div class="flex items-start space-x-3 flex-1 min-w-0">
                    <div class={format!("p-2.5 rounded-lg flex-shrink-0 {}",
                        if selected { "bg-purple-200" } else { "bg-purple-100" }
                    )}>
                        {if selected {
                            html! {
                                <svg class="w-5 h-5 text-purple-700" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                            }
                        } else {
                            html! {
                                <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            }
                        }}
                    </div>
                    <div class="flex-1 min-w-0">
                        <h5 class="font-semibold text-gray-900 text-base leading-tight mb-1 overflow-hidden">
                            <span class="block truncate" title={weapon.name.clone()}>{&weapon.name}</span>
                        </h5>
                        <p class="text-sm text-gray-600 leading-relaxed overflow-hidden">
                            <span class="block truncate" title={weapon.description.clone()}>{&weapon.description}</span>
                        </p>
                    </div>
                </div>

                // Right section: Price + Status
                <div class="flex flex-col items-end gap-2 flex-shrink-0">
                    <div class="text-right">
                        <p class="font-bold text-purple-900 text-base whitespace-nowrap">
                            {if weapon.price > 0 {
                                format!("+{}", format_currency(weapon.price))
                            } else {
                                "Miễn phí".to_string()
                            }}
                        </p>
                    </div>
                    <span class={format!("inline-flex items-center px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap {}",
                        if weapon.available { "bg-green-100 text-green-800" } else { "bg-red-100 text-red-800" }
                    )}>
                        {if weapon.available { "✓ Có sẵn" } else { "✗ Hết hàng" }}
                    </span>
                </div>
            </div>
        </div>
    }
}

#[function_component(EnhancedWeaponAccessoryCard)]
pub fn enhanced_weapon_accessory_card(props: &WeaponAccessoryCardProps) -> Html {
    let weapon = &props.weapon;

    html! {
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-purple-200 dark:border-purple-700 hover:border-purple-400 dark:hover:border-purple-500 hover:shadow-md transition-all duration-200 p-4">
            <div class="flex items-start justify-between gap-4">
                // Left section: Icon + Content
                <div class="flex items-start space-x-3 flex-1 min-w-0">
                    <div class="p-2.5 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex-shrink-0 transition-colors duration-200">
                        <svg class="w-5 h-5 text-purple-600 dark:text-purple-400 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h5 class="font-semibold text-gray-900 dark:text-gray-100 text-base leading-tight mb-1 overflow-hidden transition-colors duration-200">
                            <span class="block truncate" title={weapon.name.clone()}>{&weapon.name}</span>
                        </h5>
                        <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed overflow-hidden transition-colors duration-200">
                            <span class="block truncate" title={weapon.description.clone()}>{&weapon.description}</span>
                        </p>
                    </div>
                </div>

                // Right section: Price + Status
                <div class="flex flex-col items-end gap-2 flex-shrink-0">
                    <div class="text-right">
                        <p class="font-bold text-purple-900 dark:text-purple-100 text-base whitespace-nowrap transition-colors duration-200">
                            {if weapon.price > 0 {
                                format!("+{}", format_currency(weapon.price))
                            } else {
                                "Miễn phí".to_string()
                            }}
                        </p>
                    </div>
                    <span class={format!("inline-flex items-center px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap transition-colors duration-200 {}",
                        if weapon.available { "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300" } else { "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300" }
                    )}>
                        {if weapon.available { "✓ Có sẵn" } else { "✗ Hết hàng" }}
                    </span>
                </div>
            </div>
        </div>
    }
}

#[derive(Properties, PartialEq)]
pub struct ShoeOptionCardProps {
    pub shoe: ShoeOption,
}

#[function_component(ShoeOptionCard)]
pub fn shoe_option_card(props: &ShoeOptionCardProps) -> Html {
    let shoe = &props.shoe;

    html! {
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
            <div>
                <h5 class="text-sm font-medium text-gray-900">{&shoe.size}</h5>
            </div>
            <div class="text-right">
                <p class="text-sm font-medium text-gray-900">
                    {if let Some(price) = shoe.price {
                        format!("+{}", format_currency(price))
                    } else {
                        "Miễn phí".to_string()
                    }}
                </p>
                <p class="text-xs text-gray-500">
                    {if shoe.available { "Có sẵn" } else { "Hết hàng" }}
                </p>
            </div>
        </div>
    }
}

#[function_component(EnhancedShoeOptionCard)]
pub fn enhanced_shoe_option_card(props: &ShoeOptionCardProps) -> Html {
    let shoe = &props.shoe;

    html! {
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-green-200 dark:border-green-700 hover:border-green-400 dark:hover:border-green-500 hover:shadow-md transition-all duration-200 p-4">
            <div class="flex items-start justify-between gap-4">
                // Left section: Icon + Content
                <div class="flex items-start space-x-3 flex-1 min-w-0">
                    <div class="p-2.5 bg-green-100 dark:bg-green-900/30 rounded-lg flex-shrink-0 transition-colors duration-200">
                        <svg class="w-5 h-5 text-green-600 dark:text-green-400 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                        </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h5 class="font-semibold text-gray-900 dark:text-gray-100 text-base leading-tight mb-1 transition-colors duration-200">
                            {"Giày cosplay"}
                        </h5>
                        <div class="flex items-center gap-2">
                            <span class="text-sm text-gray-600 dark:text-gray-300 transition-colors duration-200">{"Kích thước:"}</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-sm font-medium text-gray-800 dark:text-gray-200 transition-colors duration-200">
                                {&shoe.size}
                            </span>
                        </div>
                    </div>
                </div>

                // Right section: Price + Status
                <div class="flex flex-col items-end gap-2 flex-shrink-0">
                    <div class="text-right">
                        <p class="font-bold text-green-900 dark:text-green-100 text-base whitespace-nowrap transition-colors duration-200">
                            {if let Some(price) = shoe.price {
                                format!("+{}", format_currency(price))
                            } else {
                                "Miễn phí".to_string()
                            }}
                        </p>
                    </div>
                    <span class={format!("inline-flex items-center px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap transition-colors duration-200 {}",
                        if shoe.available { "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300" } else { "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300" }
                    )}>
                        {if shoe.available { "✓ Có sẵn" } else { "✗ Hết hàng" }}
                    </span>
                </div>
            </div>
        </div>
    }
}

// Legacy exports for compatibility
pub struct ProductCard;
pub struct ProductGrid;
pub struct ProductFilter;
