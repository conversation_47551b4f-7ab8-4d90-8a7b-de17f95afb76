@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
    transition: color-scheme 0.2s ease-in-out;
  }

  body {
    @apply text-gray-900 dark:text-gray-100 leading-relaxed;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold leading-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  h5 {
    @apply text-lg md:text-xl;
  }

  h6 {
    @apply text-base md:text-lg;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-primary-600 dark:bg-primary-600 text-white hover:bg-primary-700 dark:hover:bg-primary-700 focus:ring-primary-500 dark:focus:ring-primary-400 shadow-anime;
  }

  .btn-secondary {
    @apply btn bg-secondary-600 dark:bg-secondary-600 text-white hover:bg-secondary-700 dark:hover:bg-secondary-700 focus:ring-secondary-500 dark:focus:ring-secondary-400 shadow-pink;
  }

  .btn-accent {
    @apply btn bg-accent-500 dark:bg-accent-500 text-white hover:bg-accent-600 dark:hover:bg-accent-600 focus:ring-accent-500 dark:focus:ring-accent-400;
  }

  .btn-outline {
    @apply btn border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400 hover:bg-primary-600 dark:hover:bg-primary-600 hover:text-white focus:ring-primary-500 dark:focus:ring-primary-400;
  }

  .btn-ghost {
    @apply btn text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-gray-500 dark:focus:ring-gray-400;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  /* Card Components */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200;
  }

  .card-anime {
    @apply card shadow-anime hover:shadow-anime-lg transition-shadow duration-300;
  }

  .card-product {
    @apply card-anime hover:scale-105 transition-transform duration-300 cursor-pointer;
  }

  /* Input Components */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .input-error {
    @apply input border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500;
  }

  /* Enhanced Navigation Components */
  .nav-link {
    @apply text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }

  .nav-link-enhanced {
    @apply text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 relative;
  }

  .nav-link-enhanced:hover {
    @apply shadow-sm transform translate-y-[-1px];
  }

  .nav-link-active {
    @apply nav-link text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20;
  }

  .dropdown-item-enhanced {
    @apply block p-3 rounded-lg text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-200 border border-transparent hover:border-primary-200 dark:hover:border-primary-700;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200;
  }

  .badge-primary {
    @apply badge bg-primary-100 dark:bg-primary-800 text-primary-800 dark:text-primary-100 shadow-md dark:shadow-lg border border-primary-200 dark:border-primary-600;
  }

  .badge-secondary {
    @apply badge bg-secondary-100 dark:bg-secondary-700 text-secondary-800 dark:text-secondary-100 shadow-md dark:shadow-lg border border-secondary-200 dark:border-secondary-500;
  }

  .badge-success {
    @apply badge bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200;
  }

  .badge-warning {
    @apply badge bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200;
  }

  .badge-error {
    @apply badge bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200;
  }

  /* Loading Components */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 dark:border-gray-600 border-t-primary-600 dark:border-t-primary-400;
  }

  /* Gradient Text */
  .text-gradient {
    @apply bg-gradient-anime bg-clip-text text-transparent;
  }

  /* Container */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}
