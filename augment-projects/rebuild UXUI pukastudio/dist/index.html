<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Puka Studio - Cosplay Costume Rental</title>
    <meta name="description" content="Puka Studio - Premium cosplay costume rental service specializing in anime and game character costumes in Ho Chi Minh City, Vietnam." />
    <meta name="keywords" content="cosplay, costume rental, anime, manga, games, Vietnam, Ho Chi Minh City" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://pukastudio.shop/" />
    <meta property="og:title" content="Puka Studio - Cosplay Costume Rental" />
    <meta property="og:description" content="Premium cosplay costume rental service specializing in anime and game character costumes." />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://pukastudio.shop/" />
    <meta property="twitter:title" content="Puka Studio - Cosplay Costume Rental" />
    <meta property="twitter:description" content="Premium cosplay costume rental service specializing in anime and game character costumes." />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/svg+xml" href="/images/logo.svg" />

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/tailwind-d9c1b958727ceda6.css" integrity="sha384-uMY6Darvp3CkeYOs72sj/gasHH/Uf19MMIbzFJBQ+rqshit8hiB/tpjk2LumPopm"/>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/main-6bab2c21a6038ae3.css" integrity="sha384-cFoDkysEgY7aic7YIAxdVpYAynYw8JTZg+qJ0DOCrLluuh+lOLuiKti7sfad/cy1"/>

    <!-- Copy static assets -->
    
    

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
<link rel="modulepreload" href="/puka-studio-f70b23e42dadf7ad.js" crossorigin="anonymous" integrity="sha384-yvzL3nlOYYf+b8lElGlNDtcqesUhAsLoIbE9yWfQ7nvOIE5ARIkbkT1OIsKc/tjL"><link rel="preload" href="/puka-studio-f70b23e42dadf7ad_bg.wasm" crossorigin="anonymous" integrity="sha384-+uZsUMF9/hMrcPDUR5YlMp/ZEbb6TA82VY87f6R1ylLTgBw6mhYPI8ICtg9lycbq" as="fetch" type="application/wasm"></head>
<body class="font-sans antialiased bg-gray-50">
    <div id="app"></div>

    <!-- Loading indicator -->
    <div id="loading" class="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading Puka Studio...</p>
        </div>
    </div>

    <script>
        // Hide loading indicator when app loads
        window.addEventListener('load', function() {
            const loading = document.getElementById('loading');
            if (loading) {
                loading.style.display = 'none';
            }
        });
    </script>

<script type="module">
import init, * as bindings from '/puka-studio-f70b23e42dadf7ad.js';
const wasm = await init({ module_or_path: '/puka-studio-f70b23e42dadf7ad_bg.wasm' });


window.wasmBindings = bindings;


dispatchEvent(new CustomEvent("TrunkApplicationStarted", {detail: {wasm}}));

</script></body>
</html>
